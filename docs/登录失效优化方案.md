# 登录失效优化方案

## 问题描述

原始问题：当用户token失效时，在调用`TenantHelper.ignore()`方法时会抛出`NotLoginException`，但被包装成`RuntimeException`，导致前端收到的错误信息不够友好。

## 解决方案

### 1. 全局异常处理器优化

**文件**: `hengjian-common/hengjian-common-security/src/main/java/com/hengjian/common/security/handler/GlobalExceptionHandler.java`

**修改内容**:
- 增加了`getLoginExceptionMessage()`方法，根据不同的登录异常类型返回友好的提示信息
- 支持以下异常类型的友好提示：
  - `NOT_TOKEN`: "未提供登录凭证，请先登录"
  - `INVALID_TOKEN`: "登录凭证无效，请重新登录"
  - `TOKEN_TIMEOUT`: "登录已过期，请重新登录"
  - `BE_REPLACED`: "您的账号在其他地方登录，请重新登录"
  - `KICK_OUT`: "您已被强制下线，请重新登录"
  - 默认: "登录状态失效，请重新登录"

### 2. TenantHelper.ignore()方法优化

**文件**: `hengjian-common/hengjian-common-tenant/src/main/java/com/hengjian/common/tenant/helper/TenantHelper.java`

**修改内容**:
- 修改了所有4个`ignore()`方法重载版本
- 增加了登录异常的特殊处理逻辑：
  - 直接捕获`NotLoginException`并重新抛出
  - 检查嵌套异常中的`NotLoginException`并提取抛出
  - 避免将登录异常包装成`RuntimeException`

**修改的方法**:
1. `ignore(Runnable handle)`
2. `ignore(Runnable handle, TenantType... tenantType)`
3. `ignore(Supplier<T> handle)`
4. `ignore(Supplier<T> handle, TenantType... tenantType)`

## 效果

### 修改前
```json
{
  "code": 500,
  "msg": "nested exception is org.apache.ibatis.exceptions.PersistenceException: ### Error querying database. Cause: cn.dev33.satoken.exception.NotLoginException: token 无效：eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 修改后
```json
{
  "code": 401,
  "msg": "登录凭证无效，请重新登录"
}
```

## 前端处理建议

前端可以根据返回的状态码进行处理：

```javascript
// 拦截器中处理401状态码
if (response.status === 401) {
  // 清除本地token
  localStorage.removeItem('token');
  // 跳转到登录页
  router.push('/login');
  // 显示友好提示
  Message.error(response.data.msg || '登录状态失效，请重新登录');
}
```

## 优势

1. **用户体验优化**: 提供清晰、友好的错误提示信息
2. **统一异常处理**: 所有登录相关异常都通过全局异常处理器统一处理
3. **代码健壮性**: 避免了异常被错误包装的问题
4. **维护性**: 集中管理登录异常的处理逻辑

## 测试建议

1. 测试token过期场景
2. 测试token无效场景
3. 测试并发登录被踢出场景
4. 测试强制下线场景
5. 验证前端能正确处理401状态码和友好提示信息
